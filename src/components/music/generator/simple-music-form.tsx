"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { TechButton } from "@/components/ui/tech-button";
import { Tech<PERSON>ard, TechCardContent } from "@/components/ui/tech-card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Loader2, Music, Sparkles, Clock, Zap, ChevronDown, ChevronUp, Settings, Wand2, CreditCard } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

const musicFormSchema = z.object({
  prompt: z.string().min(10, "Please describe your music idea").max(500, "Description too long"),
  duration: z.enum(["15", "30", "60"]),
  bpm: z.number().min(60).max(200).optional(),
  genres: z.array(z.string()).max(3, "Select up to 3 genres").optional(),
  moods: z.array(z.string()).max(3, "Select up to 3 moods").optional(),
  instruments: z.array(z.string()).max(3, "Select up to 3 instruments").optional(),
  themes: z.array(z.string()).max(2, "Select up to 2 themes").optional(),
});

type MusicFormValues = z.infer<typeof musicFormSchema>;

interface SimpleMusicFormProps {
  onSubmit: (values: MusicFormValues) => Promise<void>;
  isLoading?: boolean;
  userCredits?: number;
}

const DURATION_OPTIONS = [
  { value: "15", label: "15s", credits: 1, description: "Quick loop" },
  { value: "30", label: "30s", credits: 2, description: "Standard" },
  { value: "60", label: "60s", credits: 3, description: "Extended" },
];

const BPM_PRESETS = [
  { value: 70, label: "Slow", description: "Chill, ambient" },
  { value: 90, label: "Medium", description: "Pop, rock" },
  { value: 120, label: "Standard", description: "Dance, electronic" },
  { value: 140, label: "Fast", description: "Techno, EDM" },
  { value: 160, label: "Very Fast", description: "Drum & bass" },
];

// Genre options based on the image
const GENRE_OPTIONS = [
  "Ambient", "Electronic", "Corporate", "Chill Out", "Cinematic", "Modern", "Orchestral", "Rock",
  "Folk", "Jazz", "World", "Commercial", "Driving", "Upbeat", "Inspiring", "Business", "Video Game",
  "Dark", "Pop", "Trailer", "Documentary", "Soundtrack", "Fashion", "Acoustic", "Movie", "Tv",
  "High Tech", "Industrial", "Dance", "Video", "Vlog", "Marketing", "Game", "Radio", "Promotional",
  "Sports", "Party", "Summer", "Beauty"
];

// Mood options based on the image
const MOOD_OPTIONS = [
  "Smooth", "Tranquil", "Mellow", "Relaxed", "Peaceful", "Calm", "Ethereal", "Serene",
  "Contemplative", "Soothing", "Positive", "Energetic", "Bright", "Optimistic", "Hopeful", "Cool",
  "Dreamy", "Fun", "Light", "Powerful", "Confident", "Joyful", "Dramatic", "Playful", "Soft",
  "Groovy", "Reflective", "Easy", "Lively", "Romantic", "Intense", "Elegant", "Emotional",
  "Sentimental", "Proud", "Passionate", "Sweet", "Mystical", "Cheerful", "Casual", "Beautiful",
  "Melancholy", "Sad", "Aggressive", "Haunting", "Adventure", "Sincere", "Funky", "Funny"
];

// Instrument options based on the image
const INSTRUMENT_OPTIONS = [
  "Piano", "Drums", "Guitar", "Percussion", "Synth", "Electric Guitar", "Acoustic Guitar", "Bass Guitar",
  "Brass", "Violin", "Cello", "Flute", "Organ", "Trumpet", "Ukulele", "Saxophone", "Double Bass",
  "Harp", "Glockenspiel", "Synthesizer", "Keyboard", "Marimba", "Bass", "Banjo", "Strings"
];

// Theme options based on the image
const THEME_OPTIONS = [
  "Meditation", "Lifestyle", "Technology", "Progress", "Journey", "Discovery", "Inspirational",
  "Motivational", "Achievement", "Every Day", "Love", "Drama", "Children", "Hope", "Fantasy",
  "Holiday", "Health", "Family", "Real Estate", "Media", "Kids", "Science", "Education", "World",
  "Vacation", "Training", "Christmas", "Sales"
];

// Quick presets for common combinations
const QUICK_PRESETS = [
  {
    name: "Corporate",
    description: "Professional business content",
    genres: ["Corporate", "Modern"],
    moods: ["Confident", "Optimistic"],
    instruments: ["Piano", "Strings"],
    themes: ["Business", "Achievement"]
  },
  {
    name: "Cinematic",
    description: "Epic movie-style music",
    genres: ["Cinematic", "Orchestral"],
    moods: ["Dramatic", "Powerful"],
    instruments: ["Strings", "Brass"],
    themes: ["Adventure", "Journey"]
  },
  {
    name: "Gaming",
    description: "Video game background music",
    genres: ["Electronic", "Video Game"],
    moods: ["Energetic", "Fun"],
    instruments: ["Synth", "Drums"],
    themes: ["Technology", "Fantasy"]
  },
  {
    name: "Relaxing",
    description: "Calm and peaceful music",
    genres: ["Ambient", "Chill Out"],
    moods: ["Peaceful", "Serene"],
    instruments: ["Piano", "Flute"],
    themes: ["Meditation", "Health"]
  },
  {
    name: "Upbeat Pop",
    description: "Energetic popular music",
    genres: ["Pop", "Upbeat"],
    moods: ["Joyful", "Energetic"],
    instruments: ["Guitar", "Drums"],
    themes: ["Every Day", "Lifestyle"]
  }
];

export default function SimpleMusicForm({ onSubmit, isLoading = false, userCredits = 0 }: SimpleMusicFormProps) {
  const [selectedBpm, setSelectedBpm] = useState<number | undefined>(undefined);
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);
  const [selectedMoods, setSelectedMoods] = useState<string[]>([]);
  const [selectedInstruments, setSelectedInstruments] = useState<string[]>([]);
  const [selectedThemes, setSelectedThemes] = useState<string[]>([]);

  const form = useForm<MusicFormValues>({
    resolver: zodResolver(musicFormSchema),
    defaultValues: {
      prompt: "",
      duration: "30",
      bpm: undefined,
      genres: [],
      moods: [],
      instruments: [],
      themes: [],
    },
  });

  const watchedDuration = form.watch("duration");
  const selectedDurationOption = DURATION_OPTIONS.find(opt => opt.value === watchedDuration);
  const requiredCredits = selectedDurationOption?.credits || 2;
  const canGenerate = userCredits >= requiredCredits;

  const handleSubmit = async (values: MusicFormValues) => {
    if (!canGenerate) {
      toast.error(`Need ${requiredCredits} credits, you have ${userCredits}`);
      return;
    }

    try {
      await onSubmit(values);
      toast.success("Music generation started!");
      form.reset();
      setSelectedBpm(undefined);
      setSelectedGenres([]);
      setSelectedMoods([]);
      setSelectedInstruments([]);
      setSelectedThemes([]);
    } catch (error) {
      toast.error("Failed to start generation");
      console.error("Generation error:", error);
    }
  };

  const selectBpm = (bpm: number) => {
    setSelectedBpm(bpm);
    form.setValue("bpm", bpm);
  };

  const clearBpm = () => {
    setSelectedBpm(undefined);
    form.setValue("bpm", undefined);
  };

  const toggleSelection = (item: string, type: 'genres' | 'moods' | 'instruments' | 'themes', maxCount: number) => {
    const currentSelection = type === 'genres' ? selectedGenres :
                           type === 'moods' ? selectedMoods :
                           type === 'instruments' ? selectedInstruments :
                           selectedThemes;

    const setSelection = type === 'genres' ? setSelectedGenres :
                        type === 'moods' ? setSelectedMoods :
                        type === 'instruments' ? setSelectedInstruments :
                        setSelectedThemes;

    if (currentSelection.includes(item)) {
      const newSelection = currentSelection.filter(i => i !== item);
      setSelection(newSelection);
      form.setValue(type, newSelection);
    } else if (currentSelection.length < maxCount) {
      const newSelection = [...currentSelection, item];
      setSelection(newSelection);
      form.setValue(type, newSelection);
    } else {
      toast.error(`You can select up to ${maxCount} ${type}`);
    }
  };

  const applyPreset = (preset: typeof QUICK_PRESETS[0]) => {
    setSelectedGenres(preset.genres);
    setSelectedMoods(preset.moods);
    setSelectedInstruments(preset.instruments);
    setSelectedThemes(preset.themes);

    form.setValue("genres", preset.genres);
    form.setValue("moods", preset.moods);
    form.setValue("instruments", preset.instruments);
    form.setValue("themes", preset.themes);

    toast.success(`Applied ${preset.name} preset`);
  };

  const clearAllSelections = () => {
    setSelectedGenres([]);
    setSelectedMoods([]);
    setSelectedInstruments([]);
    setSelectedThemes([]);

    form.setValue("genres", []);
    form.setValue("moods", []);
    form.setValue("instruments", []);
    form.setValue("themes", []);
  };

  return (
    <TechCard variant="glass" className="w-full max-w-2xl mx-auto">
      <TechCardContent className="p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            {/* Music Description */}
            <FormField
              control={form.control}
              name="prompt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">What music do you want?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Upbeat electronic music for workout videos..."
                      className="min-h-[120px] text-base resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quick Presets */}
            <div className="space-y-3">
              <FormLabel className="text-lg font-medium flex items-center gap-2">
                <Wand2 className="h-4 w-4" />
                Quick Presets
              </FormLabel>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                {QUICK_PRESETS.map((preset) => (
                  <button
                    key={preset.name}
                    type="button"
                    onClick={() => applyPreset(preset)}
                    className="p-3 rounded-lg border transition-all duration-200 text-center hover:border-primary hover:bg-primary/5"
                  >
                    <div className="font-medium text-sm">{preset.name}</div>
                    <div className="text-xs text-muted-foreground">{preset.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Duration Selection */}
            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">Duration</FormLabel>
                  <div className="grid grid-cols-3 gap-3">
                    {DURATION_OPTIONS.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => field.onChange(option.value)}
                        className={cn(
                          "p-4 rounded-lg border-2 transition-all duration-200 text-center",
                          field.value === option.value
                            ? "border-primary bg-primary/10 text-primary"
                            : "border-border hover:border-primary/50 hover:bg-muted/50"
                        )}
                      >
                        <div className="font-semibold text-lg">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                        <div className="flex items-center justify-center gap-1 mt-2">
                          <Zap className="h-3 w-3" />
                          <span className="text-xs">{option.credits}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Advanced Options */}
            <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
              <CollapsibleTrigger asChild>
                <button
                  type="button"
                  className="flex items-center justify-between w-full p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    <span className="font-medium">Advanced Options</span>
                    {(selectedGenres.length + selectedMoods.length + selectedInstruments.length + selectedThemes.length) > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {selectedGenres.length + selectedMoods.length + selectedInstruments.length + selectedThemes.length} selected
                      </Badge>
                    )}
                  </div>
                  {isAdvancedOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>
              </CollapsibleTrigger>

              <CollapsibleContent className="space-y-6 mt-4">
                {/* Genre Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-base font-medium">🎵 Genre (up to 3)</FormLabel>
                    {selectedGenres.length > 0 && (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedGenres([]);
                          form.setValue("genres", []);
                        }}
                        className="text-sm text-muted-foreground hover:text-primary"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {GENRE_OPTIONS.map((genre) => (
                      <Badge
                        key={genre}
                        variant={selectedGenres.includes(genre) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80 transition-colors"
                        onClick={() => toggleSelection(genre, 'genres', 3)}
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Mood Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-base font-medium">😊 Mood (up to 3)</FormLabel>
                    {selectedMoods.length > 0 && (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedMoods([]);
                          form.setValue("moods", []);
                        }}
                        className="text-sm text-muted-foreground hover:text-primary"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {MOOD_OPTIONS.map((mood) => (
                      <Badge
                        key={mood}
                        variant={selectedMoods.includes(mood) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80 transition-colors"
                        onClick={() => toggleSelection(mood, 'moods', 3)}
                      >
                        {mood}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Instruments Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-base font-medium">🎹 Instruments (up to 3)</FormLabel>
                    {selectedInstruments.length > 0 && (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedInstruments([]);
                          form.setValue("instruments", []);
                        }}
                        className="text-sm text-muted-foreground hover:text-primary"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {INSTRUMENT_OPTIONS.map((instrument) => (
                      <Badge
                        key={instrument}
                        variant={selectedInstruments.includes(instrument) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80 transition-colors"
                        onClick={() => toggleSelection(instrument, 'instruments', 3)}
                      >
                        {instrument}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Theme Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-base font-medium">🎬 Theme (up to 2)</FormLabel>
                    {selectedThemes.length > 0 && (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedThemes([]);
                          form.setValue("themes", []);
                        }}
                        className="text-sm text-muted-foreground hover:text-primary"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {THEME_OPTIONS.map((theme) => (
                      <Badge
                        key={theme}
                        variant={selectedThemes.includes(theme) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80 transition-colors"
                        onClick={() => toggleSelection(theme, 'themes', 2)}
                      >
                        {theme}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Clear All Button */}
                {(selectedGenres.length + selectedMoods.length + selectedInstruments.length + selectedThemes.length) > 0 && (
                  <div className="flex justify-center">
                    <button
                      type="button"
                      onClick={clearAllSelections}
                      className="text-sm text-muted-foreground hover:text-primary underline"
                    >
                      Clear All Selections
                    </button>
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>

            {/* BPM Selection */}
            <FormField
              control={form.control}
              name="bpm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">
                    Tempo (Optional)
                    {selectedBpm && (
                      <button
                        type="button"
                        onClick={clearBpm}
                        className="ml-2 text-sm text-primary hover:underline"
                      >
                        Clear
                      </button>
                    )}
                  </FormLabel>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                    {BPM_PRESETS.map((preset) => (
                      <button
                        key={preset.value}
                        type="button"
                        onClick={() => selectBpm(preset.value)}
                        className={cn(
                          "p-3 rounded-lg border transition-all duration-200 text-center",
                          selectedBpm === preset.value
                            ? "border-primary bg-primary/10 text-primary"
                            : "border-border hover:border-primary/50 hover:bg-muted/50"
                        )}
                      >
                        <div className="font-medium">{preset.label}</div>
                        <div className="text-xs text-muted-foreground">{preset.value} BPM</div>
                      </button>
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Leave empty for automatic tempo detection
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cost and Generate Button */}
            <div className="space-y-4">
              <div className={cn(
                "p-4 rounded-lg border transition-colors",
                canGenerate
                  ? "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800"
                  : "bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800"
              )}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Cost: {requiredCredits} credits</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className={cn(
                      "h-4 w-4",
                      canGenerate ? "text-green-600" : "text-red-600"
                    )} />
                    <span className={cn(
                      "text-sm font-medium",
                      canGenerate ? "text-green-600" : "text-red-600"
                    )}>
                      {userCredits} available
                    </span>
                  </div>
                </div>

                {!canGenerate && (
                  <div className="mt-3 pt-3 border-t border-red-200 dark:border-red-800">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-red-700 dark:text-red-300">
                        Need {requiredCredits - userCredits} more credits
                      </span>
                      <button
                        type="button"
                        onClick={() => {
                          const callbackUrl = encodeURIComponent('/generate');
                          window.open(`/pricing?callback=${callbackUrl}`, '_blank');
                        }}
                        className="text-sm text-red-600 hover:text-red-700 underline font-medium"
                      >
                        Get Credits →
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <TechButton
                type="submit"
                variant="neon"
                size="xl"
                className="w-full"
                loading={isLoading}
                disabled={!canGenerate}
              >
                {isLoading ? (
                  "Generating..."
                ) : !canGenerate ? (
                  <>
                    <CreditCard className="mr-2 h-5 w-5" />
                    Need More Credits
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Generate Music
                  </>
                )}
              </TechButton>
            </div>
          </form>
        </Form>
      </TechCardContent>
    </TechCard>
  );
}
